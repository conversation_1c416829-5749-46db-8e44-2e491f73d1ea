"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { CreditCard, Coins, Zap, Star, Crown, Gem, AlertCircle, Info } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import type { User } from "firebase/auth"

interface BuyTubecoinsSectionProps {
  user: User & { tubeCoins?: number }
}

const tubecoinPackages = [
  {
    id: "starter",
    name: "Starter Pack",
    tubecoins: 100,
    price: 4.99,
    bonus: 0,
    popular: false,
    icon: <Coins className="h-6 w-6" />,
    color: "bg-gray-100",
  },
  {
    id: "popular",
    name: "Popular Pack",
    tubecoins: 500,
    price: 19.99,
    bonus: 50,
    popular: true,
    icon: <Zap className="h-6 w-6" />,
    color: "bg-blue-100",
  },
  {
    id: "premium",
    name: "Premium Pack",
    tubecoins: 1000,
    price: 34.99,
    bonus: 150,
    popular: false,
    icon: <Star className="h-6 w-6" />,
    color: "bg-purple-100",
  },
  {
    id: "ultimate",
    name: "Ultimate Pack",
    tubecoins: 2500,
    price: 79.99,
    bonus: 500,
    popular: false,
    icon: <Crown className="h-6 w-6" />,
    color: "bg-yellow-100",
  },
  {
    id: "mega",
    name: "Mega Pack",
    tubecoins: 5000,
    price: 149.99,
    bonus: 1500,
    popular: false,
    icon: <Gem className="h-6 w-6" />,
    color: "bg-gradient-to-br from-purple-100 to-pink-100",
  },
]

const paymentMethods = [
  {
    id: "card",
    name: "Credit/Debit Card",
    icon: "💳",
    description: "Visa, Mastercard, American Express (via Stripe)",
    processingFee: 2.9,
    disabled: false,
  },
  {
    id: "paypal",
    name: "PayPal",
    icon: "🅿️",
    description: "Pay with your PayPal account (via Stripe)",
    processingFee: 3.5,
    disabled: false,
  },
  {
    id: "crypto",
    name: "Cryptocurrency",
    icon: "₿",
    description: "Bitcoin, Ethereum, USDT (Coming Soon)",
    processingFee: 1.0,
    disabled: true,
  },
  {
    id: "applepay",
    name: "Apple Pay",
    icon: "🍎",
    description: "Quick payment with Touch ID (via Stripe)",
    processingFee: 2.9,
    disabled: false,
  },
  {
    id: "googlepay",
    name: "Google Pay",
    icon: "🔵",
    description: "Fast and secure payments (via Stripe)",
    processingFee: 2.9,
    disabled: false,
  },
]

export function BuyTubecoinsSection({ user }: BuyTubecoinsSectionProps) {
  const [selectedPackage, setSelectedPackage] = useState<string>("")
  const [selectedPayment, setSelectedPayment] = useState<string>("")
  const [isProcessing, setIsProcessing] = useState(false)

  const handlePurchase = async () => {
    if (!selectedPackage || !selectedPayment) return

    setIsProcessing(true)

    // Simulate payment processing
    setTimeout(() => {
      setIsProcessing(false)
      // In a real app, you would process the payment here
      alert("Purchase successful! Tubecoins have been added to your account.")
    }, 3000)
  }

  const selectedPkg = tubecoinPackages.find((pkg) => pkg.id === selectedPackage)

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Buy Tubecoins
          </CardTitle>
          <CardDescription>
            Purchase Tubecoins to promote your videos and boost your visibility on the platform.
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Current Balance */}
      <Card className="bg-gradient-to-r from-primary/10 to-primary/5">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Current Balance</p>
              <p className="text-3xl font-bold flex items-center gap-2">
                <Coins className="h-8 w-8 text-yellow-500" />
                {user.tubeCoins || 0} Tubecoins
              </p>
            </div>
            <div className="text-right">
              <p className="text-sm text-muted-foreground">Estimated Value</p>
              <p className="text-lg font-semibold">${((user.tubeCoins || 0) * 0.05).toFixed(2)}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Package Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Choose a Package</CardTitle>
          <CardDescription>Select the amount of Tubecoins you want to purchase</CardDescription>
        </CardHeader>
        <CardContent>
          <RadioGroup value={selectedPackage} onValueChange={setSelectedPackage}>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {tubecoinPackages.map((pkg) => (
                <div key={pkg.id} className="relative">
                  {pkg.popular && (
                    <Badge className="absolute -top-2 left-1/2 transform -translate-x-1/2 z-10">Most Popular</Badge>
                  )}
                  <Label htmlFor={pkg.id} className="cursor-pointer">
                    <Card
                      className={`transition-all hover:shadow-lg ${
                        selectedPackage === pkg.id ? "ring-2 ring-primary" : ""
                      } ${pkg.popular ? "border-primary" : ""}`}
                    >
                      <CardContent className="p-6">
                        <div className="flex items-center space-x-2 mb-4">
                          <RadioGroupItem value={pkg.id} id={pkg.id} />
                          <div className={`p-2 rounded-lg ${pkg.color}`}>{pkg.icon}</div>
                        </div>
                        <h3 className="font-bold text-lg mb-2">{pkg.name}</h3>
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="text-2xl font-bold">{pkg.tubecoins.toLocaleString()}</span>
                            <span className="text-sm text-muted-foreground">Tubecoins</span>
                          </div>
                          {pkg.bonus > 0 && (
                            <div className="flex items-center justify-between text-green-600">
                              <span className="text-sm font-medium">+{pkg.bonus} Bonus</span>
                              <Badge variant="secondary" className="text-green-600">
                                Free
                              </Badge>
                            </div>
                          )}
                          <div className="pt-2 border-t">
                            <div className="flex items-center justify-between">
                              <span className="text-2xl font-bold">${pkg.price}</span>
                              <span className="text-xs text-muted-foreground">
                                ${((pkg.price / (pkg.tubecoins + pkg.bonus)) * 100).toFixed(2)}/100 coins
                              </span>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </Label>
                </div>
              ))}
            </div>
          </RadioGroup>
        </CardContent>
      </Card>

      {/* Payment Method */}
      {selectedPackage && (
        <Card>
          <CardHeader>
            <CardTitle>Payment Method</CardTitle>
            <CardDescription>Choose how you want to pay for your Tubecoins</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert className="bg-blue-50 border-blue-200">
              <Info className="h-4 w-4 text-blue-600" />
              <AlertDescription className="text-blue-800">
                All payments are securely processed through Stripe, our trusted payment provider.
              </AlertDescription>
            </Alert>

            <div className="space-y-3">
              {paymentMethods.map((method) => (
                <Card
                  key={method.id}
                  className={`cursor-pointer hover:shadow-md transition-shadow ${
                    method.disabled ? "opacity-60" : ""
                  } ${selectedPayment === method.id ? "ring-2 ring-primary" : ""}`}
                  onClick={() => {
                    if (!method.disabled) {
                      setSelectedPayment(method.id)
                    }
                  }}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <span className="text-2xl">{method.icon}</span>
                        <div>
                          <p className="font-medium">{method.name}</p>
                          <p className="text-sm text-muted-foreground">{method.description}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        {method.disabled ? (
                          <Badge variant="outline" className="text-amber-600 border-amber-300 bg-amber-50">
                            Coming Soon
                          </Badge>
                        ) : (
                          <Badge variant="secondary">{method.processingFee}% fee</Badge>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {selectedPayment === "crypto" && (
              <Alert variant="destructive" className="bg-amber-50 border-amber-200">
                <AlertCircle className="h-4 w-4 text-amber-600" />
                <AlertDescription className="text-amber-800">
                  Cryptocurrency payments are currently under development. We're working on integrating a secure crypto
                  payment solution.
                </AlertDescription>
              </Alert>
            )}

            {selectedPkg && selectedPayment && (
              <div className="bg-muted/50 p-4 rounded-lg space-y-2">
                <h4 className="font-medium">Order Summary</h4>
                <div className="flex justify-between text-sm">
                  <span>{selectedPkg.name}</span>
                  <span>{selectedPkg.tubecoins.toLocaleString()} Tubecoins</span>
                </div>
                {selectedPkg.bonus > 0 && (
                  <div className="flex justify-between text-sm text-green-600">
                    <span>Bonus Tubecoins</span>
                    <span>+{selectedPkg.bonus.toLocaleString()}</span>
                  </div>
                )}
                <div className="border-t pt-2 flex justify-between font-bold">
                  <span>Total</span>
                  <span>${selectedPkg.price}</span>
                </div>
                <div className="text-xs text-muted-foreground">
                  You will receive {(selectedPkg.tubecoins + selectedPkg.bonus).toLocaleString()} Tubecoins
                </div>
              </div>
            )}

            <Button
              className="w-full"
              onClick={handlePurchase}
              disabled={
                !selectedPayment || isProcessing || paymentMethods.find((m) => m.id === selectedPayment)?.disabled
              }
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                  Processing Payment...
                </>
              ) : (
                <>
                  <CreditCard className="mr-2 h-4 w-4" />
                  Complete Purchase
                </>
              )}
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
