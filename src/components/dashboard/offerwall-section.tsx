"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Gift, Coins, ExternalLink, Star, Users, TrendingUp, Shield, Zap, Target, Award } from "lucide-react"
import type { User } from "firebase/auth"

interface OfferwallSectionProps {
  user: User & { tubeCoins?: number }
}

const offerwallProviders = [
  {
    id: "adgem",
    name: "AdGem",
    description: "Premium offers with high payouts",
    icon: <Shield className="h-5 w-5" />,
    color: "bg-blue-500",
    textColor: "text-blue-600",
    bgColor: "bg-blue-50",
    stats: {
      totalOffers: 150,
      avgPayout: 45,
      completionRate: 92,
      rating: 4.8,
    },
    features: ["High-paying surveys", "App downloads", "Gaming offers", "Video content"],
    iframeUrl: "https://wall.adgem.com/", // Placeholder URL
    isActive: true,
  },
  {
    id: "wannads",
    name: "Wannads",
    description: "Diverse task opportunities",
    icon: <Zap className="h-5 w-5" />,
    color: "bg-purple-500",
    textColor: "text-purple-600",
    bgColor: "bg-purple-50",
    stats: {
      totalOffers: 120,
      avgPayout: 35,
      completionRate: 88,
      rating: 4.6,
    },
    features: ["Quick tasks", "Social media", "Shopping offers", "Mobile apps"],
    iframeUrl: "https://wannads.com/", // Placeholder URL
    isActive: true,
  },
  {
    id: "revlum",
    name: "Revlum",
    description: "Gaming and entertainment focused",
    icon: <Target className="h-5 w-5" />,
    color: "bg-green-500",
    textColor: "text-green-600",
    bgColor: "bg-green-50",
    stats: {
      totalOffers: 95,
      avgPayout: 55,
      completionRate: 85,
      rating: 4.7,
    },
    features: ["Gaming challenges", "Video streaming", "App trials", "Loyalty programs"],
    iframeUrl: "https://revlum.com/", // Placeholder URL
    isActive: true,
  },
  {
    id: "ayet",
    name: "Ayet-Studios",
    description: "Creative and media tasks",
    icon: <Award className="h-5 w-5" />,
    color: "bg-orange-500",
    textColor: "text-orange-600",
    bgColor: "bg-orange-50",
    stats: {
      totalOffers: 80,
      avgPayout: 40,
      completionRate: 90,
      rating: 4.5,
    },
    features: ["Creative tasks", "Media consumption", "Product reviews", "Content creation"],
    iframeUrl: "https://ayet-studios.com/", // Placeholder URL
    isActive: true,
  },
]

export function OfferwallSection({ user }: OfferwallSectionProps) {
  const [activeProvider, setActiveProvider] = useState("adgem")
  const [loadingProvider, setLoadingProvider] = useState<string | null>(null)

  const handleProviderChange = (providerId: string) => {
    setLoadingProvider(providerId)
    setActiveProvider(providerId)
    // Simulate loading time
    setTimeout(() => {
      setLoadingProvider(null)
    }, 1500)
  }

  const currentProvider = offerwallProviders.find((p) => p.id === activeProvider)

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Gift className="h-5 w-5" />
            Offerwall Partners
          </CardTitle>
          <CardDescription>
            Complete offers from our trusted partners to earn Tubecoins. Each provider offers unique opportunities with
            different reward structures and task types.
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Provider Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {offerwallProviders.map((provider) => (
          <Card
            key={provider.id}
            className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
              activeProvider === provider.id ? "ring-2 ring-primary shadow-lg" : ""
            }`}
            onClick={() => handleProviderChange(provider.id)}
          >
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className={`p-2 rounded-lg ${provider.bgColor}`}>
                  <div className={provider.textColor}>{provider.icon}</div>
                </div>
                <div className="flex items-center gap-1">
                  <Star className="h-3 w-3 text-yellow-500 fill-current" />
                  <span className="text-xs font-medium">{provider.stats.rating}</span>
                </div>
              </div>
              <div>
                <CardTitle className="text-base">{provider.name}</CardTitle>
                <CardDescription className="text-xs">{provider.description}</CardDescription>
              </div>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex justify-between text-xs">
                <span className="text-muted-foreground">Offers:</span>
                <span className="font-medium">{provider.stats.totalOffers}</span>
              </div>
              <div className="flex justify-between text-xs">
                <span className="text-muted-foreground">Avg Payout:</span>
                <span className="font-medium text-green-600">{provider.stats.avgPayout} TC</span>
              </div>
              <div className="flex justify-between text-xs">
                <span className="text-muted-foreground">Success Rate:</span>
                <span className="font-medium">{provider.stats.completionRate}%</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Provider Details and Iframe */}
      <Tabs value={activeProvider} onValueChange={handleProviderChange} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          {offerwallProviders.map((provider) => (
            <TabsTrigger key={provider.id} value={provider.id} className="flex items-center gap-2">
              {provider.icon}
              <span className="hidden sm:inline">{provider.name}</span>
            </TabsTrigger>
          ))}
        </TabsList>

        {offerwallProviders.map((provider) => (
          <TabsContent key={provider.id} value={provider.id} className="space-y-4">
            {/* Provider Info */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className={`p-3 rounded-lg ${provider.bgColor}`}>
                      <div className={provider.textColor}>{provider.icon}</div>
                    </div>
                    <div>
                      <CardTitle className="flex items-center gap-2">
                        {provider.name}
                        <Badge variant="secondary" className="text-xs">
                          {provider.stats.totalOffers} offers
                        </Badge>
                      </CardTitle>
                      <CardDescription>{provider.description}</CardDescription>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="flex items-center gap-1 mb-1">
                      <Star className="h-4 w-4 text-yellow-500 fill-current" />
                      <span className="font-bold">{provider.stats.rating}</span>
                    </div>
                    <p className="text-xs text-muted-foreground">User Rating</p>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                  <div className="text-center">
                    <div className="flex items-center justify-center gap-1 mb-1">
                      <Gift className="h-4 w-4 text-blue-500" />
                      <span className="font-bold text-lg">{provider.stats.totalOffers}</span>
                    </div>
                    <p className="text-xs text-muted-foreground">Total Offers</p>
                  </div>
                  <div className="text-center">
                    <div className="flex items-center justify-center gap-1 mb-1">
                      <Coins className="h-4 w-4 text-green-500" />
                      <span className="font-bold text-lg">{provider.stats.avgPayout}</span>
                    </div>
                    <p className="text-xs text-muted-foreground">Avg Tubecoins</p>
                  </div>
                  <div className="text-center">
                    <div className="flex items-center justify-center gap-1 mb-1">
                      <TrendingUp className="h-4 w-4 text-purple-500" />
                      <span className="font-bold text-lg">{provider.stats.completionRate}%</span>
                    </div>
                    <p className="text-xs text-muted-foreground">Success Rate</p>
                  </div>
                  <div className="text-center">
                    <div className="flex items-center justify-center gap-1 mb-1">
                      <Users className="h-4 w-4 text-indigo-500" />
                      <span className="font-bold text-lg">4.2k</span>
                    </div>
                    <p className="text-xs text-muted-foreground">Active Users</p>
                  </div>
                </div>

                {/* Features */}
                <div className="space-y-2">
                  <h4 className="font-medium text-sm">Available Task Types:</h4>
                  <div className="flex flex-wrap gap-2">
                    {provider.features.map((feature, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {feature}
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Iframe Container */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ExternalLink className="h-5 w-5" />
                  {provider.name} Offerwall
                </CardTitle>
                <CardDescription>
                  Complete offers directly from {provider.name}. All rewards will be automatically credited to your
                  account.
                </CardDescription>
              </CardHeader>
              <CardContent>
                {loadingProvider === provider.id ? (
                  <div className="flex items-center justify-center h-96 bg-muted/20 rounded-lg">
                    <div className="text-center">
                      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto mb-4"></div>
                      <p className="text-lg font-medium">Loading {provider.name} offers...</p>
                      <p className="text-sm text-muted-foreground">
                        Please wait while we fetch the latest opportunities
                      </p>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <Alert>
                      <Shield className="h-4 w-4" />
                      <AlertDescription>
                        You're about to access {provider.name}'s offerwall. Complete offers to earn Tubecoins that will
                        be automatically added to your account. Make sure to follow the offer requirements carefully.
                      </AlertDescription>
                    </Alert>

                    <div className="border rounded-lg overflow-hidden">
                      <iframe
                        src={`${provider.iframeUrl}?user_id=${user.uid}&platform=impulsatube`}
                        className="w-full h-96 md:h-[600px]"
                        title={`${provider.name} Offerwall`}
                        sandbox="allow-scripts allow-same-origin allow-forms allow-popups"
                        loading="lazy"
                      />
                    </div>

                    <div className="flex items-center justify-between p-3 bg-muted/20 rounded-lg">
                      <div className="flex items-center gap-2">
                        <Shield className="h-4 w-4 text-green-600" />
                        <span className="text-sm font-medium">Secure Connection</span>
                      </div>
                      <Button variant="outline" size="sm" asChild>
                        <a href={provider.iframeUrl} target="_blank" rel="noopener noreferrer">
                          <ExternalLink className="h-3 w-3 mr-1" />
                          Open in New Tab
                        </a>
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  )
}
