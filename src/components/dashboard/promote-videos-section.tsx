"use client"

import type React from "react"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Upload, Eye, Coins, Plus, ExternalLink } from "lucide-react"
import type { User } from "firebase/auth"

interface PromoteVideosSectionProps {
  user: User & { tubeCoins?: number }
}

const mockPromotedVideos = [
  {
    id: "1",
    title: "My Latest Gaming Setup Tour",
    url: "https://youtube.com/watch?v=example1",
    thumbnail: "/placeholder.svg?height=180&width=320",
    tubecoinsSpent: 50,
    views: 1247,
    status: "active",
    timeRemaining: "2 days",
  },
  {
    id: "2",
    title: "Cooking Tutorial: Perfect Pasta",
    url: "https://youtube.com/watch?v=example2",
    thumbnail: "/placeholder.svg?height=180&width=320",
    tubecoinsSpent: 30,
    views: 856,
    status: "active",
    timeRemaining: "5 hours",
  },
  {
    id: "3",
    title: "Travel Vlog: Amazing Sunset",
    url: "https://youtube.com/watch?v=example3",
    thumbnail: "/placeholder.svg?height=180&width=320",
    tubecoinsSpent: 25,
    views: 432,
    status: "completed",
    timeRemaining: "Completed",
  },
]

export function PromoteVideosSection({ user }: PromoteVideosSectionProps) {
  const [showAddForm, setShowAddForm] = useState(false)
  const [videoUrl, setVideoUrl] = useState("")
  const [tubecoinsToSpend, setTubecoinsToSpend] = useState("")
  const [promotionDuration, setPromotionDuration] = useState("")

  const handleSubmitPromotion = (e: React.FormEvent) => {
    e.preventDefault()
    // In a real app, you would submit the promotion here
    console.log("Promoting video:", { videoUrl, tubecoinsToSpend, promotionDuration })
    setShowAddForm(false)
    setVideoUrl("")
    setTubecoinsToSpend("")
    setPromotionDuration("")
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5" />
                Promote Your YouTube Videos
              </CardTitle>
              <CardDescription>
                Use your Tubecoins to boost the visibility of your YouTube videos and reach more viewers.
              </CardDescription>
            </div>
            <Button onClick={() => setShowAddForm(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Video
            </Button>
          </div>
        </CardHeader>
      </Card>

      {showAddForm && (
        <Card>
          <CardHeader>
            <CardTitle>Promote a New Video</CardTitle>
            <CardDescription>
              Enter your YouTube video URL and choose how many Tubecoins to spend on promotion.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmitPromotion} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="videoUrl">YouTube Video URL</Label>
                <Input
                  id="videoUrl"
                  placeholder="https://youtube.com/watch?v=..."
                  value={videoUrl}
                  onChange={(e) => setVideoUrl(e.target.value)}
                  required
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="tubecoins">Tubecoins to Spend</Label>
                  <Input
                    id="tubecoins"
                    type="number"
                    placeholder="50"
                    value={tubecoinsToSpend}
                    onChange={(e) => setTubecoinsToSpend(e.target.value)}
                    max={user.tubeCoins || 0}
                    required
                  />
                  <p className="text-xs text-muted-foreground">Available: {user.tubeCoins || 0} Tubecoins</p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="duration">Promotion Duration</Label>
                  <Select value={promotionDuration} onValueChange={setPromotionDuration} required>
                    <SelectTrigger>
                      <SelectValue placeholder="Select duration" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1 Day</SelectItem>
                      <SelectItem value="3">3 Days</SelectItem>
                      <SelectItem value="7">1 Week</SelectItem>
                      <SelectItem value="14">2 Weeks</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="flex gap-2">
                <Button type="submit">Start Promotion</Button>
                <Button type="button" variant="outline" onClick={() => setShowAddForm(false)}>
                  Cancel
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Your Promoted Videos</h3>
        {mockPromotedVideos.length === 0 ? (
          <Card>
            <CardContent className="text-center py-8">
              <Upload className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">You haven't promoted any videos yet.</p>
              <Button className="mt-4" onClick={() => setShowAddForm(true)}>
                Promote Your First Video
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {mockPromotedVideos.map((video) => (
              <Card key={video.id}>
                <div className="relative">
                  <img
                    src={video.thumbnail || "/placeholder.svg"}
                    alt={video.title}
                    className="w-full h-32 object-cover"
                  />
                  <div className="absolute top-2 right-2">
                    <Badge variant={video.status === "active" ? "default" : "secondary"}>{video.status}</Badge>
                  </div>
                </div>
                <CardContent className="p-4">
                  <h4 className="font-semibold text-sm mb-2 line-clamp-2">{video.title}</h4>
                  <div className="space-y-2 text-xs">
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground">Spent:</span>
                      <div className="flex items-center gap-1">
                        <Coins className="h-3 w-3" />
                        {video.tubecoinsSpent}
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground">Views:</span>
                      <div className="flex items-center gap-1">
                        <Eye className="h-3 w-3" />
                        {video.views.toLocaleString()}
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground">Time:</span>
                      <span>{video.timeRemaining}</span>
                    </div>
                  </div>
                  <Button variant="outline" size="sm" className="w-full mt-3" asChild>
                    <a href={video.url} target="_blank" rel="noopener noreferrer">
                      <ExternalLink className="mr-2 h-3 w-3" />
                      View on YouTube
                    </a>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
