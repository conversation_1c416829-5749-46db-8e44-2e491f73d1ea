"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Users, Copy, Share2, Gift, Coins, UserPlus, Trophy, Star } from "lucide-react"
import type { User } from "firebase/auth"

interface ReferralSystemProps {
  user: User & { tubeCoins?: number }
}

const mockReferrals = [
  {
    id: "1",
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "/placeholder.svg?height=40&width=40",
    joinDate: "2024-01-15",
    status: "active",
    earned: 25,
    level: 2,
  },
  {
    id: "2",
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "/placeholder.svg?height=40&width=40",
    joinDate: "2024-01-20",
    status: "active",
    earned: 25,
    level: 1,
  },
  {
    id: "3",
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "/placeholder.svg?height=40&width=40",
    joinDate: "2024-01-25",
    status: "pending",
    earned: 0,
    level: 0,
  },
]

const referralTiers = [
  {
    level: 1,
    name: "Bronze Referrer",
    minReferrals: 1,
    bonus: 25,
    icon: <Coins className="h-5 w-5" />,
    color: "text-orange-600",
    bgColor: "bg-orange-100",
  },
  {
    level: 2,
    name: "Silver Referrer",
    minReferrals: 5,
    bonus: 30,
    icon: <Star className="h-5 w-5" />,
    color: "text-gray-600",
    bgColor: "bg-gray-100",
  },
  {
    level: 3,
    name: "Gold Referrer",
    minReferrals: 10,
    bonus: 35,
    icon: <Trophy className="h-5 w-5" />,
    color: "text-yellow-600",
    bgColor: "bg-yellow-100",
  },
]

export function ReferralSystem({ user }: ReferralSystemProps) {
  const [copied, setCopied] = useState(false)
  
  const referralCode = `TUBE${user.uid?.slice(-6).toUpperCase() || "123456"}`
  const referralLink = `https://impulsatube.com/register?ref=${referralCode}`
  
  const totalReferrals = mockReferrals.length
  const activeReferrals = mockReferrals.filter(r => r.status === "active").length
  const totalEarned = mockReferrals.reduce((sum, r) => sum + r.earned, 0)
  
  const currentTier = referralTiers.find(tier => totalReferrals >= tier.minReferrals) || referralTiers[0]
  const nextTier = referralTiers.find(tier => totalReferrals < tier.minReferrals)

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error("Failed to copy: ", err)
    }
  }

  const shareReferral = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: "Join ImpulsaTube Pro",
          text: "Earn Tubecoins by watching and promoting videos!",
          url: referralLink,
        })
      } catch (err) {
        console.error("Error sharing: ", err)
      }
    } else {
      copyToClipboard(referralLink)
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Referral Program
          </CardTitle>
          <CardDescription>
            Invite friends and earn 25 Tubecoins for each successful referral. The more you refer, the more you earn!
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Users className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-2xl font-bold">{totalReferrals}</p>
                <p className="text-sm text-muted-foreground">Total Referrals</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <UserPlus className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-2xl font-bold">{activeReferrals}</p>
                <p className="text-sm text-muted-foreground">Active Users</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Coins className="h-5 w-5 text-yellow-600" />
              </div>
              <div>
                <p className="text-2xl font-bold">{totalEarned}</p>
                <p className="text-sm text-muted-foreground">Tubecoins Earned</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Current Tier */}
      <Card>
        <CardHeader>
          <CardTitle>Your Referrer Status</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-3">
            <div className={`p-3 rounded-lg ${currentTier.bgColor}`}>
              <div className={currentTier.color}>{currentTier.icon}</div>
            </div>
            <div>
              <h3 className="font-semibold">{currentTier.name}</h3>
              <p className="text-sm text-muted-foreground">
                Earn {currentTier.bonus} Tubecoins per referral
              </p>
            </div>
          </div>
          
          {nextTier && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Progress to {nextTier.name}</span>
                <span>{totalReferrals}/{nextTier.minReferrals}</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-primary h-2 rounded-full transition-all duration-300"
                  style={{ width: `${Math.min(100, (totalReferrals / nextTier.minReferrals) * 100)}%` }}
                ></div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Referral Link */}
      <Card>
        <CardHeader>
          <CardTitle>Your Referral Link</CardTitle>
          <CardDescription>Share this link with friends to earn Tubecoins</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Input value={referralLink} readOnly className="flex-1" />
            <Button onClick={() => copyToClipboard(referralLink)} variant="outline">
              <Copy className="h-4 w-4 mr-2" />
              {copied ? "Copied!" : "Copy"}
            </Button>
            <Button onClick={shareReferral}>
              <Share2 className="h-4 w-4 mr-2" />
              Share
            </Button>
          </div>
          
          <div className="text-center">
            <p className="text-sm text-muted-foreground mb-2">Your referral code:</p>
            <Badge variant="secondary" className="text-lg px-4 py-2">{referralCode}</Badge>
          </div>
        </CardContent>
      </Card>

      {/* Referrals List */}
      <Card>
        <CardHeader>
          <CardTitle>Your Referrals ({totalReferrals})</CardTitle>
        </CardHeader>
        <CardContent>
          {mockReferrals.length === 0 ? (
            <div className="text-center py-8">
              <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">No referrals yet. Start sharing your link!</p>
            </div>
          ) : (
            <div className="space-y-4">
              {mockReferrals.map((referral) => (
                <div key={referral.id} className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={referral.avatar} alt={referral.name} />
                      <AvatarFallback>{referral.name.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium">{referral.name}</p>
                      <p className="text-sm text-muted-foreground">
                        Joined {new Date(referral.joinDate).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge variant={referral.status === "active" ? "default" : "secondary"}>
                      {referral.status}
                    </Badge>
                    <p className="text-sm text-muted-foreground mt-1">
                      +{referral.earned} TC earned
                    </p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
