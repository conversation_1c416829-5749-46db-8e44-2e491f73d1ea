"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { ShoppingBag, Gift, Star, Crown, Zap, Award } from "lucide-react"
import type { User } from "firebase/auth"

interface RewardsSectionProps {
  user: User & { tubeCoins?: number }
}

const rewardItems = [
  {
    id: "amazon-10",
    name: "Amazon Gift Card",
    description: "$10 Amazon Gift Card",
    cost: 200,
    category: "Gift Cards",
    icon: <Gift className="h-6 w-6" />,
    image: "/placeholder.svg?height=100&width=100",
    popular: true,
    inStock: true,
  },
  {
    id: "paypal-5",
    name: "PayPal Cash",
    description: "$5 PayPal Transfer",
    cost: 100,
    category: "Cash",
    icon: <Star className="h-6 w-6" />,
    image: "/placeholder.svg?height=100&width=100",
    popular: false,
    inStock: true,
  },
  {
    id: "netflix-1month",
    name: "Netflix Premium",
    description: "1 Month Netflix Premium",
    cost: 300,
    category: "Subscriptions",
    icon: <Crown className="h-6 w-6" />,
    image: "/placeholder.svg?height=100&width=100",
    popular: false,
    inStock: true,
  },
  {
    id: "spotify-3months",
    name: "Spotify Premium",
    description: "3 Months Spotify Premium",
    cost: 250,
    category: "Subscriptions",
    icon: <Zap className="h-6 w-6" />,
    image: "/placeholder.svg?height=100&width=100",
    popular: false,
    inStock: false,
  },
  {
    id: "steam-20",
    name: "Steam Gift Card",
    description: "$20 Steam Wallet Code",
    cost: 400,
    category: "Gaming",
    icon: <Award className="h-6 w-6" />,
    image: "/placeholder.svg?height=100&width=100",
    popular: true,
    inStock: true,
  },
]

const categories = ["All", "Gift Cards", "Cash", "Subscriptions", "Gaming"]

export function RewardsSection({ user }: RewardsSectionProps) {
  const [selectedCategory, setSelectedCategory] = useState("All")
  const [redeeming, setRedeeming] = useState<string | null>(null)

  const userBalance = user.tubeCoins || 0

  const filteredRewards = rewardItems.filter(
    (item) => selectedCategory === "All" || item.category === selectedCategory
  )

  const handleRedeem = async (itemId: string) => {
    const item = rewardItems.find((r) => r.id === itemId)
    if (!item || userBalance < item.cost) return

    setRedeeming(itemId)

    // Simulate redemption process
    setTimeout(() => {
      setRedeeming(null)
      alert(`Successfully redeemed ${item.name}! Check your email for details.`)
    }, 2000)
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ShoppingBag className="h-5 w-5" />
            Rewards Store
          </CardTitle>
          <CardDescription>
            Redeem your Tubecoins for amazing rewards including gift cards, cash, and premium subscriptions.
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Balance Card */}
      <Card className="bg-gradient-to-r from-green-50 to-blue-50">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Available Balance</p>
              <p className="text-3xl font-bold text-green-600">{userBalance} Tubecoins</p>
            </div>
            <div className="text-right">
              <p className="text-sm text-muted-foreground">Next Reward</p>
              <p className="text-lg font-semibold">
                {Math.max(0, Math.min(...rewardItems.filter(r => r.inStock).map(r => r.cost)) - userBalance)} TC away
              </p>
            </div>
          </div>
          <div className="mt-4">
            <div className="flex justify-between text-sm mb-2">
              <span>Progress to next reward</span>
              <span>{Math.min(100, (userBalance / Math.min(...rewardItems.filter(r => r.inStock).map(r => r.cost))) * 100).toFixed(0)}%</span>
            </div>
            <Progress 
              value={Math.min(100, (userBalance / Math.min(...rewardItems.filter(r => r.inStock).map(r => r.cost))) * 100)} 
              className="h-2" 
            />
          </div>
        </CardContent>
      </Card>

      {/* Category Filter */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <Badge
                key={category}
                variant={selectedCategory === category ? "default" : "secondary"}
                className="cursor-pointer"
                onClick={() => setSelectedCategory(category)}
              >
                {category}
              </Badge>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Rewards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredRewards.map((item) => (
          <Card key={item.id} className={`relative ${!item.inStock ? "opacity-60" : ""}`}>
            {item.popular && (
              <Badge className="absolute -top-2 left-4 z-10">Popular</Badge>
            )}
            {!item.inStock && (
              <Badge variant="destructive" className="absolute -top-2 right-4 z-10">Out of Stock</Badge>
            )}
            <CardContent className="p-6">
              <div className="flex items-center gap-4 mb-4">
                <div className="p-3 bg-primary/10 rounded-lg">
                  {item.icon}
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold">{item.name}</h3>
                  <p className="text-sm text-muted-foreground">{item.description}</p>
                </div>
              </div>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Cost:</span>
                  <span className="font-bold text-lg">{item.cost} TC</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Category:</span>
                  <Badge variant="outline" className="text-xs">{item.category}</Badge>
                </div>

                <Button
                  className="w-full"
                  onClick={() => handleRedeem(item.id)}
                  disabled={
                    !item.inStock || 
                    userBalance < item.cost || 
                    redeeming === item.id
                  }
                >
                  {redeeming === item.id ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                      Redeeming...
                    </>
                  ) : !item.inStock ? (
                    "Out of Stock"
                  ) : userBalance < item.cost ? (
                    `Need ${item.cost - userBalance} more TC`
                  ) : (
                    <>
                      <ShoppingBag className="mr-2 h-4 w-4" />
                      Redeem
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredRewards.length === 0 && (
        <Card>
          <CardContent className="text-center py-8">
            <ShoppingBag className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">No rewards found in this category.</p>
            <Button className="mt-4" onClick={() => setSelectedCategory("All")}>
              Show All Rewards
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
