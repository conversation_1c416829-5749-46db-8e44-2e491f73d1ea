import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { TrendingUp, Eye, Upload, Star } from "lucide-react"
import type { User } from "firebase/auth"

interface StatsCardsProps {
  user: User & { tubeCoins?: number }
}

export function StatsCards({ user }: StatsCardsProps) {
  const tubecoins = user.tubeCoins || 0

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <Card className="relative overflow-hidden">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Earned</CardTitle>
          <TrendingUp className="h-4 w-4 text-green-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600">+{tubecoins + 150}</div>
          <p className="text-xs text-muted-foreground">Tubecoins earned all time</p>
          <Progress value={75} className="mt-2" />
        </CardContent>
      </Card>

      <Card className="relative overflow-hidden">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Videos Watched</CardTitle>
          <Eye className="h-4 w-4 text-blue-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">47</div>
          <p className="text-xs text-muted-foreground">+12 from last week</p>
          <div className="flex items-center gap-1 mt-2">
            <Badge variant="secondary" className="text-xs">
              +235 Tubecoins
            </Badge>
          </div>
        </CardContent>
      </Card>

      <Card className="relative overflow-hidden">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Videos Promoted</CardTitle>
          <Upload className="h-4 w-4 text-purple-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">8</div>
          <p className="text-xs text-muted-foreground">3 currently active</p>
          <div className="flex items-center gap-1 mt-2">
            <Badge variant="secondary" className="text-xs">
              1,247 views
            </Badge>
          </div>
        </CardContent>
      </Card>

      <Card className="relative overflow-hidden">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Offers Completed</CardTitle>
          <Star className="h-4 w-4 text-orange-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">23</div>
          <p className="text-xs text-muted-foreground">+5 this week</p>
          <div className="flex items-center gap-1 mt-2">
            <Badge variant="secondary" className="text-xs">
              +1,150 Tubecoins
            </Badge>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
