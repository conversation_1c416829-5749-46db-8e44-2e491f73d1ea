"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { History, Search, Download, Filter, Coins, ArrowUpRight, ArrowDownLeft, Gift, CreditCard, Users } from "lucide-react"
import type { User } from "firebase/auth"

interface TransactionHistoryProps {
  user: User & { tubeCoins?: number }
}

const mockTransactions = [
  {
    id: "tx_001",
    type: "earned",
    description: "Watched video: Amazing Tech Review",
    amount: 5,
    date: "2024-01-28T10:30:00Z",
    status: "completed",
    category: "video_watch",
  },
  {
    id: "tx_002",
    type: "spent",
    description: "Promoted video: My Gaming Setup",
    amount: -50,
    date: "2024-01-28T09:15:00Z",
    status: "completed",
    category: "video_promotion",
  },
  {
    id: "tx_003",
    type: "earned",
    description: "Completed survey: Product Feedback",
    amount: 25,
    date: "2024-01-27T16:45:00Z",
    status: "completed",
    category: "offerwall",
  },
  {
    id: "tx_004",
    type: "earned",
    description: "Referral bonus: John Doe joined",
    amount: 25,
    date: "2024-01-27T14:20:00Z",
    status: "completed",
    category: "referral",
  },
  {
    id: "tx_005",
    type: "purchased",
    description: "Purchased Starter Pack",
    amount: 100,
    date: "2024-01-26T11:30:00Z",
    status: "completed",
    category: "purchase",
  },
  {
    id: "tx_006",
    type: "redeemed",
    description: "Redeemed $10 Amazon Gift Card",
    amount: -200,
    date: "2024-01-25T13:15:00Z",
    status: "completed",
    category: "redemption",
  },
  {
    id: "tx_007",
    type: "earned",
    description: "Daily login bonus",
    amount: 10,
    date: "2024-01-25T08:00:00Z",
    status: "completed",
    category: "bonus",
  },
  {
    id: "tx_008",
    type: "pending",
    description: "Offerwall completion pending",
    amount: 15,
    date: "2024-01-24T19:30:00Z",
    status: "pending",
    category: "offerwall",
  },
]

const transactionTypes = [
  { value: "all", label: "All Transactions" },
  { value: "earned", label: "Earned" },
  { value: "spent", label: "Spent" },
  { value: "purchased", label: "Purchased" },
  { value: "redeemed", label: "Redeemed" },
  { value: "pending", label: "Pending" },
]

const categories = [
  { value: "all", label: "All Categories" },
  { value: "video_watch", label: "Video Watching" },
  { value: "video_promotion", label: "Video Promotion" },
  { value: "offerwall", label: "Offerwall" },
  { value: "referral", label: "Referrals" },
  { value: "purchase", label: "Purchases" },
  { value: "redemption", label: "Redemptions" },
  { value: "bonus", label: "Bonuses" },
]

export function TransactionHistory({ user }: TransactionHistoryProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [typeFilter, setTypeFilter] = useState("all")
  const [categoryFilter, setCategoryFilter] = useState("all")
  const [showFilters, setShowFilters] = useState(false)

  const filteredTransactions = mockTransactions.filter((transaction) => {
    const matchesSearch = transaction.description.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesType = typeFilter === "all" || transaction.type === typeFilter
    const matchesCategory = categoryFilter === "all" || transaction.category === categoryFilter
    
    return matchesSearch && matchesType && matchesCategory
  })

  const getTransactionIcon = (type: string, category: string) => {
    switch (category) {
      case "video_watch":
      case "video_promotion":
        return <ArrowUpRight className="h-4 w-4" />
      case "offerwall":
        return <Gift className="h-4 w-4" />
      case "referral":
        return <Users className="h-4 w-4" />
      case "purchase":
        return <CreditCard className="h-4 w-4" />
      case "redemption":
        return <ArrowDownLeft className="h-4 w-4" />
      default:
        return <Coins className="h-4 w-4" />
    }
  }

  const getTransactionColor = (type: string, amount: number) => {
    if (type === "pending") return "text-yellow-600"
    return amount > 0 ? "text-green-600" : "text-red-600"
  }

  const exportTransactions = () => {
    // In a real app, this would generate and download a CSV/PDF
    alert("Transaction history exported successfully!")
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <History className="h-5 w-5" />
                Transaction History
              </CardTitle>
              <CardDescription>
                View all your Tubecoin transactions including earnings, spending, and redemptions.
              </CardDescription>
            </div>
            <Button onClick={exportTransactions} variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <ArrowUpRight className="h-4 w-4 text-green-600" />
              <div>
                <p className="text-sm text-muted-foreground">Total Earned</p>
                <p className="text-lg font-bold text-green-600">
                  +{mockTransactions.filter(t => t.amount > 0).reduce((sum, t) => sum + t.amount, 0)} TC
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <ArrowDownLeft className="h-4 w-4 text-red-600" />
              <div>
                <p className="text-sm text-muted-foreground">Total Spent</p>
                <p className="text-lg font-bold text-red-600">
                  {mockTransactions.filter(t => t.amount < 0).reduce((sum, t) => sum + Math.abs(t.amount), 0)} TC
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <History className="h-4 w-4 text-blue-600" />
              <div>
                <p className="text-sm text-muted-foreground">Total Transactions</p>
                <p className="text-lg font-bold">{mockTransactions.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Coins className="h-4 w-4 text-yellow-600" />
              <div>
                <p className="text-sm text-muted-foreground">Current Balance</p>
                <p className="text-lg font-bold">{user.tubeCoins || 0} TC</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search transactions..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2">
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {transactionTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category.value} value={category.value}>
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button variant="outline" onClick={() => setShowFilters(!showFilters)}>
                <Filter className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Transactions List */}
      <Card>
        <CardContent className="p-0">
          {filteredTransactions.length === 0 ? (
            <div className="text-center py-8">
              <History className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">No transactions found matching your criteria.</p>
            </div>
          ) : (
            <div className="divide-y">
              {filteredTransactions.map((transaction) => (
                <div key={transaction.id} className="p-4 hover:bg-muted/50 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className={`p-2 rounded-lg bg-muted ${getTransactionColor(transaction.type, transaction.amount)}`}>
                        {getTransactionIcon(transaction.type, transaction.category)}
                      </div>
                      <div>
                        <p className="font-medium">{transaction.description}</p>
                        <p className="text-sm text-muted-foreground">
                          {new Date(transaction.date).toLocaleDateString()} at{" "}
                          {new Date(transaction.date).toLocaleTimeString()}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className={`font-bold ${getTransactionColor(transaction.type, transaction.amount)}`}>
                        {transaction.amount > 0 ? "+" : ""}{transaction.amount} TC
                      </p>
                      <Badge variant={transaction.status === "completed" ? "default" : "secondary"}>
                        {transaction.status}
                      </Badge>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
