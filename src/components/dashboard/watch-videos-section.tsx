"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import { Play, Coins, Eye, Heart, MessageCircle, CheckCircle } from "lucide-react"
import { VideoSearch } from "@/components/dashboard/video-search"
import { VideoInteraction } from "@/components/dashboard/video-interaction"
import { useAuth } from "@/contexts/AuthContext"
import { doc, updateDoc, increment, arrayUnion, getDoc } from "firebase/firestore"
import { db } from "@/lib/firebase/config"
import { toast } from "sonner"
import type { User } from "firebase/auth"

interface WatchVideosSectionProps {
  user: User & { tubeCoins?: number }
}

const mockVideos = [
  {
    id: "1",
    title: "Amazing Tech Review - iPhone 15 Pro Max",
    channel: {
      name: "Tech<PERSON><PERSON>",
      avatar: "/placeholder-small.svg",
      subscribers: "2.1M"
    },
    thumbnail: "https://img.youtube.com/vi/NdYWuo9OFAw/maxresdefault.jpg",
    youtubeUrl: "https://www.youtube.com/watch?v=NdYWuo9OFAw",
    duration: "12:34",
    views: "1.2M",
    reward: 5,
    category: "Technology",
    likes: 1247,
    dislikes: 23,
    comments: 89,
    description: "Complete review of the latest iPhone with all the new features and improvements.",
    uploadDate: "2 days ago"
  },
  {
    id: "2",
    title: "Best Gaming Setup for 2024",
    channel: {
      name: "GameMaster",
      avatar: "/placeholder-small.svg",
      subscribers: "1.8M"
    },
    thumbnail: "https://img.youtube.com/vi/NdYWuo9OFAw/maxresdefault.jpg",
    youtubeUrl: "https://www.youtube.com/watch?v=NdYWuo9OFAw",
    duration: "8:45",
    views: "856K",
    reward: 5,
    category: "Gaming",
    likes: 2341,
    dislikes: 45,
    comments: 156,
    description: "Everything you need to build the perfect gaming setup this year.",
    uploadDate: "1 week ago"
  },
  {
    id: "3",
    title: "Cooking the Perfect Pasta",
    channel: {
      name: "ChefLife",
      avatar: "/placeholder-small.svg",
      subscribers: "950K"
    },
    thumbnail: "https://img.youtube.com/vi/NdYWuo9OFAw/maxresdefault.jpg",
    youtubeUrl: "https://www.youtube.com/watch?v=NdYWuo9OFAw",
    duration: "15:22",
    views: "432K",
    reward: 7,
    category: "Cooking",
    likes: 892,
    dislikes: 12,
    comments: 67,
    description: "Learn the secrets to making restaurant-quality pasta at home.",
    uploadDate: "3 days ago"
  },
  {
    id: "4",
    title: "Travel Vlog: Tokyo Adventures",
    channel: {
      name: "Wanderlust",
      avatar: "/placeholder-small.svg",
      subscribers: "3.2M"
    },
    thumbnail: "https://img.youtube.com/vi/NdYWuo9OFAw/maxresdefault.jpg",
    youtubeUrl: "https://www.youtube.com/watch?v=NdYWuo9OFAw",
    duration: "20:15",
    views: "2.1M",
    reward: 8,
    category: "Travel",
    likes: 5432,
    dislikes: 87,
    comments: 234,
    description: "Join me on an incredible journey through Tokyo's hidden gems.",
    uploadDate: "5 days ago"
  },
  {
    id: "5",
    title: "Fitness Routine for Beginners",
    channel: {
      name: "FitLife",
      avatar: "/placeholder-small.svg",
      subscribers: "1.5M"
    },
    thumbnail: "https://img.youtube.com/vi/NdYWuo9OFAw/maxresdefault.jpg",
    youtubeUrl: "https://www.youtube.com/watch?v=NdYWuo9OFAw",
    duration: "18:30",
    views: "678K",
    reward: 6,
    category: "Fitness",
    likes: 1876,
    dislikes: 34,
    comments: 123,
    description: "Start your fitness journey with this beginner-friendly workout routine.",
    uploadDate: "1 week ago"
  },
  {
    id: "6",
    title: "DIY Home Decoration Ideas",
    channel: {
      name: "HomeDesign",
      avatar: "/placeholder-small.svg",
      subscribers: "800K"
    },
    thumbnail: "https://img.youtube.com/vi/NdYWuo9OFAw/maxresdefault.jpg",
    youtubeUrl: "https://www.youtube.com/watch?v=NdYWuo9OFAw",
    duration: "14:12",
    views: "543K",
    reward: 5,
    category: "Lifestyle",
    likes: 1234,
    dislikes: 28,
    comments: 89,
    description: "Transform your home with these creative and budget-friendly decoration ideas.",
    uploadDate: "4 days ago"
  },
]

export function WatchVideosSection({ user }: WatchVideosSectionProps) {
  const { setTubeCoins, tubeCoins } = useAuth()
  const [watchingVideo, setWatchingVideo] = useState<string | null>(null)
  const [selectedVideo, setSelectedVideo] = useState<any>(null)
  const [filteredVideos, setFilteredVideos] = useState(mockVideos)
  const [watchProgress, setWatchProgress] = useState(0)
  const [watchedVideos, setWatchedVideos] = useState<string[]>([])
  const [isEarningCoins, setIsEarningCoins] = useState(false)
  const [isVideoPaused, setIsVideoPaused] = useState(false)
  const [progressInterval, setProgressInterval] = useState<NodeJS.Timeout | null>(null)

  // Load watched videos when component mounts
  useEffect(() => {
    const loadWatchedVideos = async () => {
      if (!user?.uid) return

      try {
        const userRef = doc(db, `projects/impulsatubepro/users/${user.uid}`)
        const userDoc = await getDoc(userRef)

        if (userDoc.exists()) {
          const userData = userDoc.data()
          if (userData.watchedVideos) {
            setWatchedVideos(userData.watchedVideos)
          }
        }
      } catch (error) {
        console.error("Error loading watched videos:", error)
      }
    }

    loadWatchedVideos()
  }, [user?.uid])

  // Cleanup interval on unmount
  useEffect(() => {
    return () => {
      if (progressInterval) {
        clearInterval(progressInterval)
      }
    }
  }, [progressInterval])

  const handleSearch = (query: string, filters: any) => {
    let filtered = mockVideos

    if (query) {
      filtered = filtered.filter(
        (video) =>
          video.title.toLowerCase().includes(query.toLowerCase()) ||
          video.channel.name.toLowerCase().includes(query.toLowerCase()) ||
          video.category.toLowerCase().includes(query.toLowerCase()),
      )
    }

    if (filters.category) {
      filtered = filtered.filter((video) => video.category === filters.category)
    }

    if (filters.minReward) {
      filtered = filtered.filter((video) => video.reward >= filters.minReward)
    }

    if (filters.maxReward) {
      filtered = filtered.filter((video) => video.reward <= filters.maxReward)
    }

    if (filters.sortBy) {
      switch (filters.sortBy) {
        case "newest":
          // In a real app, you'd sort by upload date
          break
        case "most_viewed":
          filtered.sort(
            (a, b) => Number.parseInt(b.views.replace(/[^\d]/g, "")) - Number.parseInt(a.views.replace(/[^\d]/g, "")),
          )
          break
        case "highest_reward":
          filtered.sort((a, b) => b.reward - a.reward)
          break
        case "lowest_reward":
          filtered.sort((a, b) => a.reward - b.reward)
          break
      }
    }

    setFilteredVideos(filtered)
  }

  const updateTubeCoinsInFirestore = async (coinsToAdd: number, videoId: string) => {
    try {
      if (!user?.uid) return false

      // Use the same path structure as AuthContext
      const userRef = doc(db, `projects/impulsatubepro/users/${user.uid}`)
      await updateDoc(userRef, {
        tubeCoins: increment(coinsToAdd),
        watchedVideos: arrayUnion(videoId),
        updatedAt: new Date()
      })

      return true
    } catch (error) {
      console.error("Error updating TubeCoins:", error)
      return false
    }
  }

  const handleWatchVideo = async (video: any) => {
    if (watchedVideos.includes(video.id)) {
      toast.error("You have already watched this video!")
      return
    }

    setWatchingVideo(video.id)
    setWatchProgress(0)
    setIsEarningCoins(false)
    setIsVideoPaused(false)

    startProgressTimer(video)
  }

  const startProgressTimer = (video: any) => {
    // Clear any existing interval
    if (progressInterval) {
      clearInterval(progressInterval)
    }

    // Get video duration based on video ID for variety
    const getVideoDuration = (videoId: string) => {
      switch (videoId) {
        case "1": return 45000 // 45 seconds
        case "2": return 60000 // 1 minute
        case "3": return 30000 // 30 seconds
        case "4": return 75000 // 1 minute 15 seconds
        case "5": return 90000 // 1 minute 30 seconds
        case "6": return 40000 // 40 seconds
        default: return 60000 // Default 1 minute
      }
    }

    const duration = getVideoDuration(video.id)
    const interval = 100 // Update every 100ms
    const steps = duration / interval

    let currentStep = Math.floor((watchProgress / 100) * steps) // Resume from current progress

    const newInterval = setInterval(() => {
      currentStep++
      const progress = (currentStep / steps) * 100
      setWatchProgress(progress)

      if (progress >= 100) {
        clearInterval(newInterval)
        setProgressInterval(null)
        completeVideoWatch(video)
      }
    }, interval)

    setProgressInterval(newInterval)
  }

  const pauseProgress = () => {
    if (progressInterval) {
      clearInterval(progressInterval)
      setProgressInterval(null)
    }
  }

  const handleVideoClick = (video: any) => {
    if (isVideoPaused) {
      // Resume video and progress
      setIsVideoPaused(false)
      startProgressTimer(video)
    } else {
      // Pause video and progress
      setIsVideoPaused(true)
      pauseProgress()
    }
  }

  const completeVideoWatch = async (video: any) => {
    setIsEarningCoins(true)

    // Update Firestore
    const success = await updateTubeCoinsInFirestore(video.reward, video.id)

    if (success) {
      // Update local state
      setTubeCoins(tubeCoins + video.reward)
      setWatchedVideos(prev => [...prev, video.id])

      toast.success(`🎉 You earned ${video.reward} TubeCoins for watching "${video.title}"!`, {
        duration: 4000,
      })
    } else {
      toast.error("Failed to update your TubeCoins. Please try again.")
    }

    // Reset states
    setTimeout(() => {
      setWatchingVideo(null)
      setWatchProgress(0)
      setIsEarningCoins(false)
      setIsVideoPaused(false)
      if (progressInterval) {
        clearInterval(progressInterval)
        setProgressInterval(null)
      }
    }, 2000)
  }

  if (selectedVideo) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Play className="h-5 w-5" />
                {selectedVideo.title}
              </CardTitle>
              <Button variant="outline" onClick={() => setSelectedVideo(null)}>
                Back to Videos
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Video Player */}
            <div className="relative aspect-video bg-black rounded-lg overflow-hidden">
              {watchingVideo === selectedVideo.id ? (
                // YouTube Embedded Player
                <div className="relative w-full h-full">
                  <iframe
                    src={`https://www.youtube.com/embed/${selectedVideo.youtubeUrl.split('v=')[1]?.split('&')[0]}?autoplay=${isVideoPaused ? '0' : '1'}&rel=0&controls=0`}
                    title={selectedVideo.title}
                    className="w-full h-full border-0"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowFullScreen
                  />

                  {/* Invisible Click Frame - Captures all user interactions */}
                  <div
                    className="absolute inset-0 z-10 cursor-pointer"
                    onClick={() => handleVideoClick(selectedVideo)}
                    title={isVideoPaused ? "Click to resume video" : "Click to pause video"}
                  />

                  {/* Pause Indicator */}
                  {isVideoPaused && (
                    <div className="absolute inset-0 bg-black/50 flex items-center justify-center z-20 pointer-events-none">
                      <div className="bg-black/80 text-white p-4 rounded-lg">
                        <Play className="h-12 w-12 mx-auto mb-2" />
                        <p className="text-sm">Video Paused</p>
                        <p className="text-xs text-gray-300">Click to resume</p>
                      </div>
                    </div>
                  )}

                  {/* Progress Overlay */}
                  {!isEarningCoins && (
                    <div className="absolute bottom-4 left-4 right-4 bg-black/80 text-white p-3 rounded-lg z-30 pointer-events-none">
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Watching Progress</span>
                          <span>{Math.round(watchProgress)}% complete</span>
                        </div>
                        <Progress value={watchProgress} className="h-2" />
                        <div className="flex justify-between text-xs">
                          <span>Keep watching to earn {selectedVideo.reward} TubeCoins!</span>
                          <span className="text-gray-300">
                            {(() => {
                              const getVideoDuration = (videoId: string) => {
                                switch (videoId) {
                                  case "1": return 45
                                  case "2": return 60
                                  case "3": return 30
                                  case "4": return 75
                                  case "5": return 90
                                  case "6": return 40
                                  default: return 60
                                }
                              }
                              const totalSeconds = getVideoDuration(selectedVideo.id)
                              const remainingSeconds = Math.ceil(totalSeconds * (100 - watchProgress) / 100)
                              return `${remainingSeconds}s left`
                            })()}
                          </span>
                        </div>
                        {isVideoPaused && (
                          <p className="text-xs text-center text-yellow-300">⏸️ Progress paused - Click to resume</p>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Completion Overlay */}
                  {isEarningCoins && (
                    <div className="absolute inset-0 bg-black/80 flex flex-col items-center justify-center z-40">
                      <div className="text-white text-center space-y-4 bg-black/60 p-8 rounded-lg">
                        <CheckCircle className="h-16 w-16 mx-auto text-green-400" />
                        <p className="text-xl font-semibold">Video Completed!</p>
                        <p className="text-lg">Earning {selectedVideo.reward} TubeCoins...</p>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                // Video Thumbnail with Play Button
                <div className="relative w-full h-full">
                  <img
                    src={selectedVideo.thumbnail}
                    alt={selectedVideo.title}
                    className="w-full h-full object-cover"
                  />

                  {/* Play Button Overlay */}
                  <div className="absolute inset-0 flex items-center justify-center bg-black/30">
                    <Button
                      size="lg"
                      className="rounded-full w-20 h-20 p-0 bg-red-600 hover:bg-red-700"
                      onClick={() => handleWatchVideo(selectedVideo)}
                      disabled={watchedVideos.includes(selectedVideo.id)}
                    >
                      {watchedVideos.includes(selectedVideo.id) ? (
                        <CheckCircle className="h-10 w-10" />
                      ) : (
                        <Play className="h-10 w-10" />
                      )}
                    </Button>
                  </div>

                  {/* Video Duration */}
                  <div className="absolute bottom-4 right-4 bg-black/80 text-white px-2 py-1 rounded text-sm">
                    {selectedVideo.duration}
                  </div>

                  {/* Watched Badge */}
                  {watchedVideos.includes(selectedVideo.id) && (
                    <div className="absolute top-4 right-4 bg-green-600 text-white px-3 py-1 rounded-full text-sm font-medium flex items-center gap-1">
                      <CheckCircle className="h-4 w-4" />
                      Watched
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Video Info */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={selectedVideo.channel.avatar || "/placeholder-small.svg"} />
                    <AvatarFallback>{selectedVideo.channel.name.charAt(0)}</AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-semibold">{selectedVideo.channel.name}</p>
                    <p className="text-sm text-muted-foreground">{selectedVideo.channel.subscribers} subscribers • {selectedVideo.views} views</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">{selectedVideo.category}</Badge>
                  <div className="flex items-center gap-1 text-green-600 font-bold">
                    <Coins className="h-4 w-4" />+{selectedVideo.reward}
                  </div>
                </div>
              </div>

              <p className="text-muted-foreground">{selectedVideo.description}</p>

              {/* Video Interactions */}
              <VideoInteraction
                videoId={selectedVideo.id}
                user={user}
                initialLikes={selectedVideo.likes}
                initialDislikes={selectedVideo.dislikes}
                initialComments={[]}
              />
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Play className="h-5 w-5" />
            Watch Videos & Earn Tubecoins
          </CardTitle>
          <CardDescription>
            Watch promoted videos to earn Tubecoins. Each video you watch completely will reward you with Tubecoins.
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Search */}
      <VideoSearch onSearch={handleSearch} />

      {/* Videos Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredVideos.map((video) => (
          <Card key={video.id} className="overflow-hidden hover:shadow-lg transition-shadow">
            <div className="relative">
              <img src={video.thumbnail} alt={video.title} className="w-full h-48 object-cover" />
              <div className="absolute bottom-2 right-2 bg-black/80 text-white px-2 py-1 rounded text-xs">
                {video.duration}
              </div>
              <div className="absolute top-2 left-2">
                <Badge variant="secondary">{video.category}</Badge>
              </div>
              {watchedVideos.includes(video.id) && (
                <div className="absolute top-2 right-2 bg-green-600 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1">
                  <CheckCircle className="h-3 w-3" />
                  Watched
                </div>
              )}
            </div>
            <CardContent className="p-4">
              <h3 className="font-semibold text-sm mb-2 line-clamp-2">{video.title}</h3>
              <div className="flex items-center gap-2 mb-3">
                <Avatar className="h-6 w-6">
                  <AvatarImage src={video.channel.avatar || "/placeholder-small.svg"} />
                  <AvatarFallback>{video.channel.name.charAt(0)}</AvatarFallback>
                </Avatar>
                <span className="text-sm text-muted-foreground">{video.channel.name}</span>
              </div>
              <div className="flex items-center justify-between text-xs text-muted-foreground mb-3">
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-1">
                    <Eye className="h-3 w-3" />
                    {video.views}
                  </div>
                  <div className="flex items-center gap-1">
                    <Heart className="h-3 w-3" />
                    {video.likes}
                  </div>
                  <div className="flex items-center gap-1">
                    <MessageCircle className="h-3 w-3" />
                    {video.comments}
                  </div>
                </div>
                <div className="flex items-center gap-1 text-green-600 font-medium">
                  <Coins className="h-3 w-3" />+{video.reward}
                </div>
              </div>
              <Button
                className="w-full"
                onClick={() => setSelectedVideo(video)}
                disabled={watchingVideo === video.id || watchedVideos.includes(video.id)}
                variant={watchedVideos.includes(video.id) ? "secondary" : "default"}
              >
                {watchingVideo === video.id ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                    Watching...
                  </>
                ) : watchedVideos.includes(video.id) ? (
                  <>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Watched
                  </>
                ) : (
                  <>
                    <Play className="mr-2 h-4 w-4" />
                    Watch & Earn
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredVideos.length === 0 && (
        <Card>
          <CardContent className="text-center py-8">
            <Play className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">No videos found matching your search criteria.</p>
            <Button className="mt-4" onClick={() => handleSearch("", {})}>
              Show All Videos
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
