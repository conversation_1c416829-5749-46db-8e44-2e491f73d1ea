
"use client";
import { Youtube } from "lucide-react";
import Link from "next/link";
import { UserNav } from "./UserNav";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { useAuth } from "@/contexts/AuthContext";

export function AppHeader() {
  const { user } = useAuth();

  return (
    <header className="sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex flex-col space-y-2 px-4 md:px-6 py-4">
        {/* Top row with logo and user nav */}
        <div className="flex h-12 items-center space-x-4 sm:justify-between sm:space-x-0">
        <div className="flex items-center gap-2">
          <div className="md:hidden">
            <SidebarTrigger />
          </div>
          <Link href="/dashboard" className="flex items-center space-x-2">
            <Youtube className="h-8 w-8 text-primary" />
            <span className="hidden font-bold sm:inline-block text-xl">
              ImpulsaTube Pro
            </span>
          </Link>
        </div>
        
          <div className="flex flex-1 items-center justify-end space-x-4">
            <UserNav />
          </div>
        </div>

        {/* Welcome message */}
        <div className="flex items-center justify-center sm:justify-start">
          <div className="text-center sm:text-left">
            <h1 className="text-lg sm:text-xl font-bold">
              Welcome back, {user?.displayName || user?.email?.split('@')[0] || 'User'}
            </h1>
            <p className="text-sm text-muted-foreground">
              Ready to earn and promote with Tubecoins?
            </p>
          </div>
        </div>
      </div>
    </header>
  );
}
