
"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  Sidebar,
  SidebarHeader,
  SidebarContent,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarFooter,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import {
  LayoutDashboard,
  Lightbulb,
  PlusCircle,
  Gift,
  BarChart3,
  UserCircle2,
  Youtube,
  Settings,
  Coins,
  Menu
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/contexts/AuthContext";

const navItems = [
  { href: "/dashboard", icon: LayoutDashboard, label: "Dashboard", tooltip: "Dashboard" },
  { href: "/ai-suggestions", icon: Lightbulb, label: "AI Suggestions", tooltip: "AI Content Helper" },
  { href: "/create-campaign", icon: PlusCircle, label: "Create Campaign", tooltip: "New Campaign" },
  { href: "/offerwall", icon: Gift, label: "Offerwall", tooltip: "Earn Coins" },
  { href: "/leaderboard", icon: BarChart3, label: "Leaderboard", tooltip: "Rankings" },
  { href: "/profile", icon: UserCircle2, label: "My Profile", tooltip: "User Profile"},
];

export function AppSidebar() {
  const pathname = usePathname();
  const { tubeCoins } = useAuth();

  return (
    <Sidebar collapsible="icon" variant="sidebar" side="left">
      <SidebarHeader className="flex items-center justify-start p-4 border-b border-sidebar-border">
        <SidebarTrigger className="h-8 w-8" />
      </SidebarHeader>
      <SidebarContent className="p-2">
        <SidebarMenu>
          {navItems.map((item) => (
            <SidebarMenuItem key={item.href}>
              <Link href={item.href} legacyBehavior passHref>
                <SidebarMenuButton
                  isActive={pathname === item.href || (item.href !== "/dashboard" && pathname.startsWith(item.href))}
                  tooltip={{ children: item.tooltip, className: "capitalize" }}
                  className="justify-start"
                >
                  <item.icon className="h-5 w-5" />
                  <span className="group-data-[collapsible=icon]:hidden">{item.label}</span>
                </SidebarMenuButton>
              </Link>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarContent>
      <SidebarFooter className="p-4 border-t border-sidebar-border">
         <div className="flex items-center justify-center group-data-[collapsible=icon]:justify-center p-2 rounded-md bg-sidebar-accent text-sidebar-accent-foreground">
            <Coins className="h-5 w-5 text-yellow-500 group-data-[collapsible=icon]:mx-auto" />
            <div className="ml-2 group-data-[collapsible=icon]:hidden">
              <p className="text-sm font-semibold">{tubeCoins?.toLocaleString() || 0}</p>
              <p className="text-xs text-muted-foreground">TubeCoins</p>
            </div>
          </div>
      </SidebarFooter>
    </Sidebar>
  );
}
